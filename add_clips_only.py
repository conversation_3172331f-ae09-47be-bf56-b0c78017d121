#!/usr/bin/env python3
"""
Add New Clips Only - No Downloads, No Temp Files
Adds new clips to existing video folder using existing source video.
Clean operation that doesn't create temporary files.
"""

import json
import subprocess
import os
from pathlib import Path

class ClipsOnlyAdder:
    def __init__(self):
        self.base_dir = Path(".")
        self.clips_dir = self.base_dir / "Clips"
        self.ffmpeg_path = self.base_dir / "ffmpeg.exe"
        
        # Check if ffmpeg exists
        if not self.ffmpeg_path.exists():
            raise FileNotFoundError(f"ffmpeg.exe not found at {self.ffmpeg_path}")

    def find_existing_video_folder(self):
        """Find the existing video folder"""
        video_folders = [d for d in self.clips_dir.iterdir() if d.is_dir() and d.name.startswith("1. N3on")]
        if not video_folders:
            raise Exception("No existing video folder found")
        return video_folders[0]

    def find_source_video(self):
        """Look for source video in current directory or ask user to provide one"""
        # Check for temp_video.mp4 first
        temp_video = self.base_dir / "temp_video.mp4"
        if temp_video.exists():
            return temp_video
        
        # Look for any mp4 files in current directory
        mp4_files = list(self.base_dir.glob("*.mp4"))
        if mp4_files:
            print(f"Found source video: {mp4_files[0].name}")
            return mp4_files[0]
        
        raise Exception("No source video found. Please ensure you have the source video file in the current directory.")

    def add_clips_to_existing_folder(self, source_video, video_folder, clips_config):
        """Add new clips to existing folder, starting from next available number"""
        print(f"📁 Adding clips to existing folder: {video_folder.name}")
        
        # Find highest existing clip number
        existing_clips = list(video_folder.glob("Clip *.mp4"))
        if existing_clips:
            clip_numbers = []
            for clip in existing_clips:
                try:
                    # Extract number from "Clip X - name.mp4"
                    num_str = clip.name.split(" - ")[0].replace("Clip ", "")
                    clip_numbers.append(int(num_str))
                except:
                    pass
            start_number = max(clip_numbers) + 1 if clip_numbers else 1
        else:
            start_number = 1
        
        print(f"🎬 Adding {len(clips_config)} new clips starting from Clip {start_number}...")
        
        for i, clip in enumerate(clips_config):
            clip_number = start_number + i
            clip_name = clip['name']
            start_time = clip['start_time']
            duration = clip['duration']
            
            # Clean filename
            safe_name = "".join(c for c in clip_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            clip_filename = f"Clip {clip_number} - {safe_name}.mp4"
            clip_path = video_folder / clip_filename
            
            print(f"✂️  Creating clip {clip_number}: {clip_name}")
            
            # Use ffmpeg to create clip
            cmd = [
                str(self.ffmpeg_path),
                "-i", str(source_video),
                "-ss", str(start_time),
                "-t", str(duration),
                "-c", "copy",
                "-avoid_negative_ts", "make_zero",
                str(clip_path),
                "-y"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"❌ Failed to create clip {clip_number}: {result.stderr}")
            else:
                print(f"✅ Clip {clip_number} created: {clip_filename}")
        
        return video_folder

    def process_new_clips(self, config_file="clips_config.json"):
        """Add new clips to existing folder"""
        # Load config
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        clips = config['clips']
        
        print("=== Adding New Clips to Existing Folder ===")
        print(f"Number of new clips: {len(clips)}")
        print()
        
        try:
            # Find existing video folder
            video_folder = self.find_existing_video_folder()
            print(f"Found existing folder: {video_folder.name}")
            
            # Find source video
            source_video = self.find_source_video()
            print(f"Using source video: {source_video.name}")
            print()
            
            # Add clips to existing folder
            self.add_clips_to_existing_folder(source_video, video_folder, clips)
            
            print()
            print(f"🎉 All done! New clips added to: {video_folder}")
            print(f"📊 Added {len(clips)} clips successfully")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
        
        return True

def main():
    adder = ClipsOnlyAdder()
    adder.process_new_clips()

if __name__ == "__main__":
    main()
